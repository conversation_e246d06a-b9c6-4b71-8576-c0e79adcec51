using LegendXManager.Shared.Services.Plugins;

namespace LegendXManager.Plugins.CatalogManagement.Configuration
{
    public class CatalogPluginConfiguration : PluginConfigurationBase
    {
        public CatalogSettings Catalog { get; set; } = new();
        public DisplaySettings Display { get; set; } = new();
        public SearchSettings Search { get; set; } = new();
        public ExportSettings Export { get; set; } = new();
        public NotificationSettings Notifications { get; set; } = new();
        public SecuritySettings Security { get; set; } = new();
        public PerformanceSettings Performance { get; set; } = new();
    }

    public class CatalogSettings
    {
        public int DefaultPageSize { get; set; } = 20;
        public int MaxPageSize { get; set; } = 100;
        public string DefaultSortBy { get; set; } = "name";
        public string DefaultSortDirection { get; set; } = "asc";
        public bool EnableCategories { get; set; } = true;
        public bool EnableTags { get; set; } = true;
        public bool EnableRatings { get; set; } = true;
        public bool EnableReviews { get; set; } = true;
        public bool EnableInventoryTracking { get; set; } = true;
        public bool EnablePriceHistory { get; set; } = true;
        public bool EnableBulkOperations { get; set; } = true;
        public int LowStockThreshold { get; set; } = 10;
        public string[] RequiredFields { get; set; } = { "Name", "Description", "Category", "Price", "SKU" };
        public string[] OptionalFields { get; set; } = { "Tags", "Supplier", "Weight", "Dimensions", "Barcode" };
    }

    public class DisplaySettings
    {
        public string DefaultView { get; set; } = "grid"; // grid, list, table
        public bool ShowProductImages { get; set; } = true;
        public bool ShowRatings { get; set; } = true;
        public bool ShowStock { get; set; } = true;
        public bool ShowPrices { get; set; } = true;
        public bool ShowSupplier { get; set; } = true;
        public bool ShowSKU { get; set; } = true;
        public bool ShowFeaturedBadge { get; set; } = true;
        public bool ShowLowStockWarning { get; set; } = true;
        public int GridColumns { get; set; } = 4;
        public string ImagePlaceholder { get; set; } = "bi bi-image";
        public Dictionary<string, string> CategoryColors { get; set; } = new()
        {
            { "Electronics", "#007acc" },
            { "Books", "#28a745" },
            { "Clothing", "#dc3545" },
            { "Home & Garden", "#fd7e14" },
            { "Sports", "#6f42c1" },
            { "Automotive", "#20c997" }
        };
    }

    public class SearchSettings
    {
        public bool EnableFullTextSearch { get; set; } = true;
        public bool EnableAutoComplete { get; set; } = true;
        public bool EnableSearchHistory { get; set; } = true;
        public bool EnableAdvancedFilters { get; set; } = true;
        public bool EnablePriceRangeFilter { get; set; } = true;
        public bool EnableCategoryFilter { get; set; } = true;
        public bool EnableStockFilter { get; set; } = true;
        public bool EnableRatingFilter { get; set; } = true;
        public bool EnableSupplierFilter { get; set; } = true;
        public int MaxSearchResults { get; set; } = 1000;
        public int AutoCompleteLimit { get; set; } = 10;
        public string[] SearchableFields { get; set; } = { "Name", "Description", "Tags", "SKU", "Supplier" };
    }

    public class ExportSettings
    {
        public bool EnableExport { get; set; } = true;
        public string[] SupportedFormats { get; set; } = { "CSV", "Excel", "JSON", "PDF" };
        public string DefaultFormat { get; set; } = "CSV";
        public bool IncludeImages { get; set; } = false;
        public bool IncludeCategories { get; set; } = true;
        public bool IncludePricing { get; set; } = true;
        public bool IncludeInventory { get; set; } = true;
        public int MaxExportRecords { get; set; } = 10000;
        public string ExportPath { get; set; } = "exports/catalog";
    }

    public class NotificationSettings
    {
        public bool EnableLowStockAlerts { get; set; } = true;
        public bool EnableOutOfStockAlerts { get; set; } = true;
        public bool EnablePriceChangeAlerts { get; set; } = false;
        public bool EnableNewProductAlerts { get; set; } = false;
        public bool EnableBulkOperationAlerts { get; set; } = true;
        public string[] NotificationRecipients { get; set; } = Array.Empty<string>();
        public Dictionary<string, bool> NotificationChannels { get; set; } = new()
        {
            { "Email", true },
            { "InApp", true },
            { "SMS", false },
            { "Webhook", false }
        };
    }

    public class SecuritySettings
    {
        public bool EnableAuditLog { get; set; } = true;
        public bool EnableDataEncryption { get; set; } = false;
        public bool EnableFieldLevelSecurity { get; set; } = true;
        public bool RequireApprovalForPriceChanges { get; set; } = false;
        public bool RequireApprovalForDeletion { get; set; } = true;
        public bool EnableDataMasking { get; set; } = false;
        public string[] SensitiveFields { get; set; } = { "CostPrice", "Supplier" };
        public Dictionary<string, string[]> RolePermissions { get; set; } = new()
        {
            { "Admin", new[] { "Create", "Read", "Update", "Delete", "Export", "Import", "BulkOperations" } },
            { "Manager", new[] { "Create", "Read", "Update", "Export", "BulkOperations" } },
            { "CatalogManager", new[] { "Create", "Read", "Update", "Export" } },
            { "Viewer", new[] { "Read" } }
        };
    }

    public class PerformanceSettings
    {
        public bool EnableCaching { get; set; } = true;
        public int CacheDurationMinutes { get; set; } = 30;
        public bool EnableLazyLoading { get; set; } = true;
        public bool EnableVirtualization { get; set; } = true;
        public bool EnableCompression { get; set; } = true;
        public int BatchSize { get; set; } = 50;
        public int MaxConcurrentOperations { get; set; } = 10;
        public bool EnableBackgroundProcessing { get; set; } = true;
        public Dictionary<string, object> DatabaseSettings { get; set; } = new()
        {
            { "ConnectionTimeout", 30 },
            { "CommandTimeout", 60 },
            { "MaxPoolSize", 100 },
            { "EnableRetry", true },
            { "RetryCount", 3 }
        };
    }
}

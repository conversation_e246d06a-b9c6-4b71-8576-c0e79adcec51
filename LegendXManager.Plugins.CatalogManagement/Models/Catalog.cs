namespace LegendXManager.Plugins.CatalogManagement.Models
{
    public class Catalog
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
        public string Tags { get; set; } = string.Empty;
        public int StockQuantity { get; set; }
        public string SKU { get; set; } = string.Empty;
        public string Supplier { get; set; } = string.Empty;
        public decimal CostPrice { get; set; }
        public string Barcode { get; set; } = string.Empty;
        public double Weight { get; set; }
        public string Dimensions { get; set; } = string.Empty;
        public bool IsFeatured { get; set; }
        public int ViewCount { get; set; }
        public double AverageRating { get; set; }
        public int ReviewCount { get; set; }
    }

    public class CatalogCategory
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = "bi bi-folder";
        public bool IsActive { get; set; } = true;
        public int SortOrder { get; set; }
        public string Color { get; set; } = "#6c757d";
        public int ProductCount { get; set; }
    }

    public class CatalogFilter
    {
        public string SearchTerm { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public bool? InStock { get; set; }
        public bool? IsFeatured { get; set; }
        public string SortBy { get; set; } = "name";
        public string SortDirection { get; set; } = "asc";
        public int PageSize { get; set; } = 20;
        public int PageNumber { get; set; } = 1;
    }
}

{"pluginId": "catalog-management", "name": "Catalog Management", "description": "Comprehensive catalog and product management system with advanced filtering, search capabilities, and analytics dashboard", "version": "1.0.0", "author": "LegendX Team", "enabled": true, "lastModified": "2024-01-01T00:00:00Z", "metadata": {"type": "Page", "category": "Business", "icon": "bi bi-grid-3x3-gap", "route": "/catalog", "componentName": "CatalogManagement", "assemblyName": "LegendXManager.Plugins.CatalogManagement", "tags": ["catalog", "products", "inventory", "business", "management"], "minimumAppVersion": "1.0.0", "dependencies": ["LegendXManager.Shared >= 1.0.0", "Microsoft.AspNetCore.Components >= 9.0.0"], "lifecycle": {"autoStart": true, "canDisable": true, "canUninstall": true, "startupOrder": "Normal", "startupDependencies": [], "shutdownDependencies": []}}, "settings": {"debugMode": false, "logLevel": "Information", "dataSource": "InMemory", "enableDemoData": true, "maxRecordsPerPage": 100}, "environment": {"development": "true", "apiBaseUrl": "https://api.legendx.com", "cdnUrl": "https://cdn.legendx.com", "supportUrl": "https://support.legendx.com/catalog-management"}, "permissions": {"requiredRoles": ["Admin", "Manager", "CatalogManager"], "requiredPermissions": ["CatalogManagement", "ProductManagement"], "requiresAuthentication": true, "allowedUsers": [], "deniedUsers": []}, "ui": {"theme": "dark", "layout": "default", "colors": {"primary": "#007acc", "secondary": "#6c757d", "success": "#28a745", "warning": "#ffc107", "danger": "#dc3545", "info": "#17a2b8"}, "customStyles": {"borderRadius": "8px", "boxShadow": "0 4px 12px rgba(0, 0, 0, 0.3)", "cardHoverTransform": "translateY(-4px)"}, "showInNavigation": true, "navigationOrder": 10, "navigationIcon": "bi bi-grid-3x3-gap", "navigationCategory": "Business"}, "features": {"enableLogging": true, "enableCaching": true, "enableAnalytics": true, "enableNotifications": true, "enableExport": true, "enableImport": true, "customFeatures": {"enableBarcodeScanning": false, "enableImageRecognition": false, "enablePriceComparison": true, "enableInventoryPrediction": false, "enableBulkEdit": true, "enableAdvancedSearch": true, "enableProductRecommendations": false}}, "catalog": {"defaultPageSize": 20, "maxPageSize": 100, "defaultSortBy": "name", "defaultSortDirection": "asc", "enableCategories": true, "enableTags": true, "enableRatings": true, "enableReviews": true, "enableInventoryTracking": true, "enablePriceHistory": true, "enableBulkOperations": true, "lowStockThreshold": 10, "requiredFields": ["Name", "Description", "Category", "Price", "SKU"], "optionalFields": ["Tags", "Supplier", "Weight", "Dimensions", "Barcode"]}, "display": {"defaultView": "grid", "showProductImages": true, "showRatings": true, "showStock": true, "showPrices": true, "showSupplier": true, "showSKU": true, "showFeaturedBadge": true, "showLowStockWarning": true, "gridColumns": 4, "imagePlaceholder": "bi bi-image", "categoryColors": {"Electronics": "#007acc", "Books": "#28a745", "Clothing": "#dc3545", "Home & Garden": "#fd7e14", "Sports": "#6f42c1", "Automotive": "#20c997"}}, "search": {"enableFullTextSearch": true, "enableAutoComplete": true, "enableSearchHistory": true, "enableAdvancedFilters": true, "enablePriceRangeFilter": true, "enableCategoryFilter": true, "enableStockFilter": true, "enableRatingFilter": true, "enableSupplierFilter": true, "maxSearchResults": 1000, "autoCompleteLimit": 10, "searchableFields": ["Name", "Description", "Tags", "SKU", "Supplier"]}, "export": {"enableExport": true, "supportedFormats": ["CSV", "Excel", "JSON", "PDF"], "defaultFormat": "CSV", "includeImages": false, "includeCategories": true, "includePricing": true, "includeInventory": true, "maxExportRecords": 10000, "exportPath": "exports/catalog"}, "notifications": {"enableLowStockAlerts": true, "enableOutOfStockAlerts": true, "enablePriceChangeAlerts": false, "enableNewProductAlerts": false, "enableBulkOperationAlerts": true, "notificationRecipients": [], "notificationChannels": {"Email": true, "InApp": true, "SMS": false, "Webhook": false}}, "security": {"enableAuditLog": true, "enableDataEncryption": false, "enableFieldLevelSecurity": true, "requireApprovalForPriceChanges": false, "requireApprovalForDeletion": true, "enableDataMasking": false, "sensitiveFields": ["CostPrice", "Supplier"], "rolePermissions": {"Admin": ["Create", "Read", "Update", "Delete", "Export", "Import", "BulkOperations"], "Manager": ["Create", "Read", "Update", "Export", "BulkOperations"], "CatalogManager": ["Create", "Read", "Update", "Export"], "Viewer": ["Read"]}}, "performance": {"enableCaching": true, "cacheDurationMinutes": 30, "enableLazyLoading": true, "enableVirtualization": true, "enableCompression": true, "batchSize": 50, "maxConcurrentOperations": 10, "enableBackgroundProcessing": true, "databaseSettings": {"connectionTimeout": 30, "commandTimeout": 60, "maxPoolSize": 100, "enableRetry": true, "retryCount": 3}}}
.catalog-management {
    padding: 20px;
    background-color: #1e1e1e;
    color: #d4d4d4;
    min-height: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Section */
.catalog-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #3e3e42;
}

.catalog-header h2 {
    color: #ffffff;
    margin: 0;
    font-size: 28px;
    font-weight: 600;
}

.catalog-header h2 i {
    color: #007acc;
    margin-right: 12px;
}

.catalog-header p {
    margin: 8px 0 0 0;
    font-size: 14px;
    color: #cccccc;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 12px 16px;
    background-color: #252526;
    border-radius: 8px;
    border: 1px solid #3e3e42;
    min-width: 80px;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #007acc;
}

.stat-label {
    display: block;
    font-size: 11px;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 4px;
}

/* Filters Section */
.catalog-filters {
    background-color: #252526;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #3e3e42;
}

.catalog-filters .form-control,
.catalog-filters .form-select {
    background-color: #2d2d30;
    border: 1px solid #3e3e42;
    color: #d4d4d4;
    font-size: 13px;
}

.catalog-filters .form-control:focus,
.catalog-filters .form-select:focus {
    background-color: #2d2d30;
    border-color: #007acc;
    color: #d4d4d4;
    box-shadow: 0 0 0 0.2rem rgba(0, 122, 204, 0.25);
}

.catalog-filters .input-group-text {
    background-color: #2d2d30;
    border: 1px solid #3e3e42;
    color: #cccccc;
}

.dropdown-menu {
    background-color: #2d2d30;
    border: 1px solid #3e3e42;
    color: #d4d4d4;
}

.dropdown-menu .form-label {
    color: #cccccc;
    font-size: 12px;
    font-weight: 600;
}

/* Loading State */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #cccccc;
}

/* Grid View */
.catalog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.catalog-card .card {
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 12px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.catalog-card .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    border-color: #007acc;
}

.card-img-placeholder {
    height: 180px;
    background: linear-gradient(135deg, #2d2d30 0%, #3e3e42 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.card-img-placeholder i {
    font-size: 48px;
    color: #666;
}

.card-img-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background-color: #ffc107;
    color: #000;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.catalog-card .card-body {
    padding: 20px;
}

.catalog-card .card-title {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 16px;
}

.catalog-card .card-text {
    font-size: 13px;
    margin-bottom: 15px;
    line-height: 1.5;
    color: #cccccc;
}

.catalog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.rating {
    display: flex;
    align-items: center;
    gap: 2px;
}

.rating i {
    font-size: 12px;
}

.price-stock {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.price {
    font-weight: 700;
    color: #4fc1ff;
    font-size: 18px;
}

.stock {
    font-size: 12px;
    font-weight: 500;
}

.catalog-details {
    margin-bottom: 15px;
    padding-top: 12px;
    border-top: 1px solid #3e3e42;
}

.catalog-details small {
    font-size: 11px;
    color: #888;
}

.card-actions {
    display: flex;
    gap: 8px;
}

/* List View */
.catalog-list .table {
    background-color: #252526;
    color: #d4d4d4;
    border: 1px solid #3e3e42;
    border-radius: 8px;
    overflow: hidden;
}

.catalog-list .table th {
    background-color: #2d2d30;
    border-color: #3e3e42;
    color: #cccccc;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 15px 12px;
}

.catalog-list .table td {
    border-color: #3e3e42;
    vertical-align: middle;
    padding: 15px 12px;
}

.catalog-list .table tbody tr:hover {
    background-color: #2a2d2e;
}

.product-thumb,
.product-thumb-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    object-fit: cover;
}

.product-thumb-placeholder {
    background-color: #2d2d30;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

/* Empty State */
.empty-state {
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 12px;
    margin-top: 20px;
}

.empty-state i {
    color: #666;
}

.empty-state h4 {
    color: #cccccc;
}

/* Pagination */
.pagination .page-link {
    background-color: #252526;
    border-color: #3e3e42;
    color: #cccccc;
}

.pagination .page-link:hover {
    background-color: #3e3e42;
    border-color: #3e3e42;
    color: #ffffff;
}

.pagination .page-item.active .page-link {
    background-color: #007acc;
    border-color: #007acc;
    color: #ffffff;
}

.pagination .page-item.disabled .page-link {
    background-color: #2d2d30;
    border-color: #3e3e42;
    color: #666;
}

/* Button Styles */
.btn-primary {
    background-color: #007acc;
    border-color: #007acc;
    font-weight: 500;
}

.btn-primary:hover {
    background-color: #005a9e;
    border-color: #005a9e;
}

.btn-outline-primary {
    color: #007acc;
    border-color: #007acc;
}

.btn-outline-primary:hover {
    background-color: #007acc;
    border-color: #007acc;
    color: white;
}

.btn-outline-secondary {
    color: #cccccc;
    border-color: #3e3e42;
}

.btn-outline-secondary:hover {
    background-color: #3e3e42;
    border-color: #3e3e42;
    color: white;
}

.btn-outline-danger {
    color: #f85149;
    border-color: #f85149;
}

.btn-outline-danger:hover {
    background-color: #f85149;
    border-color: #f85149;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .catalog-management {
        padding: 15px;
    }
    
    .catalog-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }
    
    .header-stats {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .stat-item {
        min-width: 70px;
        padding: 10px 12px;
    }
    
    .catalog-filters .row > div {
        margin-bottom: 10px;
    }
    
    .catalog-grid {
        grid-template-columns: 1fr;
    }
    
    .card-actions {
        flex-wrap: wrap;
    }
}

@media (max-width: 576px) {
    .catalog-header h2 {
        font-size: 24px;
    }
    
    .header-stats {
        width: 100%;
        justify-content: space-between;
    }
    
    .stat-item {
        flex: 1;
        min-width: 60px;
    }
    
    .stat-value {
        font-size: 20px;
    }
}

@using LegendXManager.Plugins.CatalogManagement.Models
@using LegendXManager.Plugins.CatalogManagement.Services
@inject ICatalogService CatalogService

<div class="catalog-management">
    <!-- Header Section -->
    <div class="catalog-header">
        <div class="header-left">
            <h2><i class="bi bi-grid-3x3-gap"></i> Catalog Management</h2>
            <p class="text-muted">Manage your product catalog with advanced filtering and analytics</p>
        </div>
        <div class="header-right">
            <div class="header-stats">
                @if (statistics != null)
                {
                    <div class="stat-item">
                        <span class="stat-value">@statistics["TotalProducts"]</span>
                        <span class="stat-label">Products</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">@statistics["InStock"]</span>
                        <span class="stat-label">In Stock</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value text-warning">@statistics["LowStock"]</span>
                        <span class="stat-label">Low Stock</span>
                    </div>
                }
            </div>
            <button class="btn btn-primary" @onclick="ShowAddCatalogModal">
                <i class="bi bi-plus-circle"></i> Add Product
            </button>
        </div>
    </div>

    <!-- Filters and Controls -->
    <div class="catalog-filters">
        <div class="row g-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" placeholder="Search products..." 
                           @bind="filter.SearchTerm" @oninput="OnSearchChanged" />
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" @bind="filter.Category" @onchange="OnFilterChanged">
                    <option value="">All Categories</option>
                    @foreach (var category in categories)
                    {
                        <option value="@category.Name">@category.Name (@category.ProductCount)</option>
                    }
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" @bind="filter.SortBy" @onchange="OnFilterChanged">
                    <option value="name">Name</option>
                    <option value="price">Price</option>
                    <option value="category">Category</option>
                    <option value="stock">Stock</option>
                    <option value="rating">Rating</option>
                    <option value="created">Date Added</option>
                </select>
            </div>
            <div class="col-md-2">
                <div class="btn-group w-100" role="group">
                    <button type="button" class="btn @(viewMode == "grid" ? "btn-primary" : "btn-outline-secondary")" 
                            @onclick='() => SetViewMode("grid")'>
                        <i class="bi bi-grid-3x3"></i>
                    </button>
                    <button type="button" class="btn @(viewMode == "list" ? "btn-primary" : "btn-outline-secondary")" 
                            @onclick='() => SetViewMode("list")'>
                        <i class="bi bi-list"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-funnel"></i> Filters
                    </button>
                    <div class="dropdown-menu p-3" style="min-width: 250px;">
                        <div class="mb-3">
                            <label class="form-label">Price Range</label>
                            <div class="row g-2">
                                <div class="col">
                                    <input type="number" class="form-control form-control-sm" placeholder="Min" 
                                           @bind="filter.MinPrice" @onchange="OnFilterChanged" />
                                </div>
                                <div class="col">
                                    <input type="number" class="form-control form-control-sm" placeholder="Max" 
                                           @bind="filter.MaxPrice" @onchange="OnFilterChanged" />
                                </div>
                            </div>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="showInStockOnly" @onchange="OnStockFilterChanged" />
                            <label class="form-check-label">In Stock Only</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="showFeaturedOnly" @onchange="OnFeaturedFilterChanged" />
                            <label class="form-check-label">Featured Only</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Area -->
    @if (isLoading)
    {
        <div class="loading-container">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading catalog data...</p>
        </div>
    }
    else if (catalogs.Any())
    {
        @if (viewMode == "grid")
        {
            <div class="catalog-grid">
                @foreach (var catalog in catalogs)
                {
                    <div class="catalog-card">
                        <div class="card h-100">
                            <div class="card-img-placeholder">
                                @if (!string.IsNullOrEmpty(catalog.ImageUrl))
                                {
                                    <img src="@catalog.ImageUrl" alt="@catalog.Name" class="card-img-top" />
                                }
                                else
                                {
                                    <i class="bi bi-image"></i>
                                }
                                @if (catalog.IsFeatured)
                                {
                                    <span class="featured-badge"><i class="bi bi-star-fill"></i></span>
                                }
                            </div>
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title">@catalog.Name</h6>
                                <p class="card-text text-muted flex-grow-1">@catalog.Description</p>
                                
                                <div class="catalog-meta mb-2">
                                    <span class="badge" style="background-color: @GetCategoryColor(catalog.Category)">
                                        @catalog.Category
                                    </span>
                                    <div class="rating">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <i class="bi @(i <= catalog.AverageRating ? "bi-star-fill" : "bi-star") text-warning"></i>
                                        }
                                        <small class="text-muted">(@catalog.ReviewCount)</small>
                                    </div>
                                </div>
                                
                                <div class="price-stock mb-2">
                                    <span class="price">$@catalog.Price.ToString("F2")</span>
                                    <span class="stock @(catalog.StockQuantity <= 10 ? "text-danger" : "text-success")">
                                        @catalog.StockQuantity in stock
                                    </span>
                                </div>
                                
                                <div class="catalog-details">
                                    <small class="text-muted d-block">SKU: @catalog.SKU</small>
                                    <small class="text-muted">Supplier: @catalog.Supplier</small>
                                </div>
                                
                                <div class="card-actions mt-auto pt-2">
                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewCatalog(catalog)">
                                        <i class="bi bi-eye"></i> View
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" @onclick="() => EditCatalog(catalog)">
                                        <i class="bi bi-pencil"></i> Edit
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteCatalog(catalog.Id)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="catalog-list">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Rating</th>
                                <th>SKU</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var catalog in catalogs)
                            {
                                <tr class="@(catalog.StockQuantity <= 10 ? "table-warning" : "")">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="product-image me-3">
                                                @if (!string.IsNullOrEmpty(catalog.ImageUrl))
                                                {
                                                    <img src="@catalog.ImageUrl" alt="@catalog.Name" class="product-thumb" />
                                                }
                                                else
                                                {
                                                    <div class="product-thumb-placeholder">
                                                        <i class="bi bi-image"></i>
                                                    </div>
                                                }
                                            </div>
                                            <div>
                                                <strong>@catalog.Name</strong>
                                                @if (catalog.IsFeatured)
                                                {
                                                    <i class="bi bi-star-fill text-warning ms-1"></i>
                                                }
                                                <br>
                                                <small class="text-muted">@catalog.Description</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge" style="background-color: @GetCategoryColor(catalog.Category)">
                                            @catalog.Category
                                        </span>
                                    </td>
                                    <td><strong class="text-primary">$@catalog.Price.ToString("F2")</strong></td>
                                    <td>
                                        <span class="@(catalog.StockQuantity <= 10 ? "text-danger" : "text-success")">
                                            @catalog.StockQuantity
                                        </span>
                                    </td>
                                    <td>
                                        <div class="rating">
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                <i class="bi @(i <= catalog.AverageRating ? "bi-star-fill" : "bi-star") text-warning"></i>
                                            }
                                            <small class="text-muted">(@catalog.ReviewCount)</small>
                                        </div>
                                    </td>
                                    <td><code>@catalog.SKU</code></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" @onclick="() => ViewCatalog(catalog)" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary" @onclick="() => EditCatalog(catalog)" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" @onclick="() => DeleteCatalog(catalog.Id)" title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        }

        <!-- Pagination -->
        @if (totalPages > 1)
        {
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item @(filter.PageNumber <= 1 ? "disabled" : "")">
                        <button class="page-link" @onclick="() => ChangePage(filter.PageNumber - 1)">Previous</button>
                    </li>
                    
                    @for (int i = Math.Max(1, filter.PageNumber - 2); i <= Math.Min(totalPages, filter.PageNumber + 2); i++)
                    {
                        <li class="page-item @(i == filter.PageNumber ? "active" : "")">
                            <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                        </li>
                    }
                    
                    <li class="page-item @(filter.PageNumber >= totalPages ? "disabled" : "")">
                        <button class="page-link" @onclick="() => ChangePage(filter.PageNumber + 1)">Next</button>
                    </li>
                </ul>
            </nav>
        }
    }
    else
    {
        <div class="empty-state">
            <div class="text-center p-5">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <h4 class="mt-3">No products found</h4>
                <p class="text-muted">
                    @if (!string.IsNullOrWhiteSpace(filter.SearchTerm) || !string.IsNullOrWhiteSpace(filter.Category))
                    {
                        <span>No products match your current filters. Try adjusting your search criteria.</span>
                    }
                    else
                    {
                        <span>Get started by adding your first product to the catalog.</span>
                    }
                </p>
                <div class="mt-3">
                    @if (!string.IsNullOrWhiteSpace(filter.SearchTerm) || !string.IsNullOrWhiteSpace(filter.Category))
                    {
                        <button class="btn btn-outline-secondary me-2" @onclick="ClearFilters">
                            <i class="bi bi-x-circle"></i> Clear Filters
                        </button>
                    }
                    <button class="btn btn-primary" @onclick="ShowAddCatalogModal">
                        <i class="bi bi-plus-circle"></i> Add First Product
                    </button>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<Catalog> catalogs = new();
    private List<CatalogCategory> categories = new();
    private Dictionary<string, int>? statistics;
    private CatalogFilter filter = new();
    private bool isLoading = true;
    private string viewMode = "grid";
    private bool showInStockOnly = false;
    private bool showFeaturedOnly = false;
    private int totalCount = 0;
    private int totalPages = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Load categories and statistics
            categories = await CatalogService.GetCategoriesAsync();
            statistics = await CatalogService.GetCatalogStatisticsAsync();

            // Load catalogs with current filter
            var result = await CatalogService.GetPagedCatalogsAsync(filter);
            catalogs = result.Items;
            totalCount = result.TotalCount;
            totalPages = (int)Math.Ceiling((double)totalCount / filter.PageSize);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading catalog data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task OnSearchChanged(ChangeEventArgs e)
    {
        filter.SearchTerm = e.Value?.ToString() ?? string.Empty;
        filter.PageNumber = 1; // Reset to first page
        await LoadData();
    }

    private async Task OnFilterChanged()
    {
        filter.PageNumber = 1; // Reset to first page
        await LoadData();
    }

    private async Task OnStockFilterChanged()
    {
        filter.InStock = showInStockOnly ? true : null;
        filter.PageNumber = 1;
        await LoadData();
    }

    private async Task OnFeaturedFilterChanged()
    {
        filter.IsFeatured = showFeaturedOnly ? true : null;
        filter.PageNumber = 1;
        await LoadData();
    }

    private void SetViewMode(string mode)
    {
        viewMode = mode;
    }

    private async Task ChangePage(int pageNumber)
    {
        if (pageNumber >= 1 && pageNumber <= totalPages)
        {
            filter.PageNumber = pageNumber;
            await LoadData();
        }
    }

    private async Task ClearFilters()
    {
        filter = new CatalogFilter();
        showInStockOnly = false;
        showFeaturedOnly = false;
        await LoadData();
    }

    private string GetCategoryColor(string category)
    {
        var categoryInfo = categories.FirstOrDefault(c => c.Name == category);
        return categoryInfo?.Color ?? "#6c757d";
    }

    private void ShowAddCatalogModal()
    {
        // TODO: Implement add catalog modal
        Console.WriteLine("Add catalog modal would open here");
    }

    private void ViewCatalog(Catalog catalog)
    {
        // TODO: Implement view catalog details
        Console.WriteLine($"View catalog: {catalog.Name}");
    }

    private void EditCatalog(Catalog catalog)
    {
        // TODO: Implement edit catalog modal
        Console.WriteLine($"Edit catalog: {catalog.Name}");
    }

    private async Task DeleteCatalog(int catalogId)
    {
        // TODO: Add confirmation dialog
        if (await CatalogService.DeleteCatalogAsync(catalogId))
        {
            await LoadData();
        }
    }
}

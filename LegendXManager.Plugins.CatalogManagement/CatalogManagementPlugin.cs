using LegendXManager.Shared.Services.Plugins;
using LegendXManager.Plugins.CatalogManagement.Components;
using LegendXManager.Plugins.CatalogManagement.Configuration;
using Microsoft.Extensions.DependencyInjection;
using LegendXManager.Plugins.CatalogManagement.Services;

namespace LegendXManager.Plugins.CatalogManagement
{
    public class CatalogManagementPlugin : BasePlugin
    {
        private readonly IPluginConfiguration _pluginConfiguration;
        private CatalogPluginConfiguration? _catalogConfig;

        public CatalogManagementPlugin() : this(null)
        {
        }

        public CatalogManagementPlugin(IPluginConfiguration? pluginConfiguration) : base(new PluginMetadata
        {
            Id = "catalog-management",
            Name = "Catalog Management",
            Description = "Comprehensive catalog and product management system with advanced filtering, search capabilities, and analytics dashboard",
            Version = "1.0.0",
            Author = "LegendX Team",
            Category = "Business",
            Icon = "bi bi-grid-3x3-gap",
            Type = PluginType.Page,
            RequiresAuthentication = true,
            RequiredRoles = new[] { "Admin", "Manager", "CatalogManager" },
            RequiredPermissions = new[] { "CatalogManagement", "ProductManagement" }
        })
        {
            _pluginConfiguration = pluginConfiguration ?? new PluginConfiguration("catalog-management");
        }

        public override Type ComponentType => typeof(CatalogManagement);

        public override async Task InitializeAsync()
        {
            Console.WriteLine("🔧 Initializing Catalog Management Plugin...");
            
            try
            {
                // Load plugin configuration
                await LoadConfigurationAsync();
                
                // Apply configuration to metadata if available
                if (_catalogConfig != null)
                {
                    ApplyConfigurationToMetadata(_catalogConfig);
                }

                Console.WriteLine("✅ Catalog Management Plugin initialized successfully");
                Console.WriteLine($"   Plugin: {Name} v{Version}");
                Console.WriteLine($"   Type: {Type}");
                Console.WriteLine($"   Category: {Category}");
                Console.WriteLine($"   Icon: {Icon}");
                Console.WriteLine($"   Author: {Author}");
                Console.WriteLine($"   Configuration: {(_catalogConfig != null ? "Loaded" : "Using defaults")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error initializing Catalog Management Plugin: {ex.Message}");
                throw;
            }
            
            await base.InitializeAsync();
        }

        public override async Task<bool> CanActivateAsync()
        {
            Console.WriteLine("🔍 Checking if Catalog Management Plugin can be activated...");
            
            // Check base activation requirements
            var canActivate = await base.CanActivateAsync();
            
            if (!canActivate)
            {
                Console.WriteLine("❌ Base activation requirements not met");
                return false;
            }

            try
            {
                // Check configuration-specific requirements
                if (_catalogConfig != null)
                {
                    // Check if plugin is enabled in configuration
                    if (!_catalogConfig.Enabled)
                    {
                        Console.WriteLine("❌ Plugin is disabled in configuration");
                        return false;
                    }

                    // Check minimum app version if specified
                    if (!string.IsNullOrEmpty(_catalogConfig.Metadata?.MinimumAppVersion))
                    {
                        // This would typically check against the actual app version
                        Console.WriteLine($"✓ Minimum app version requirement: {_catalogConfig.Metadata.MinimumAppVersion}");
                    }

                    // Check dependencies
                    if (_catalogConfig.Metadata?.Dependencies?.Length > 0)
                    {
                        Console.WriteLine($"✓ Dependencies: {string.Join(", ", _catalogConfig.Metadata.Dependencies)}");
                    }
                }

                Console.WriteLine("✅ Catalog Management Plugin can be activated");
                Console.WriteLine("   ✓ All dependencies are available");
                Console.WriteLine("   ✓ Required permissions are satisfied");
                Console.WriteLine("   ✓ Configuration is valid");
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error during activation check: {ex.Message}");
                return false;
            }
        }

        private async Task LoadConfigurationAsync()
        {
            try
            {
                _catalogConfig = await _pluginConfiguration.GetConfigurationAsync<CatalogPluginConfiguration>();
                
                if (_catalogConfig == null)
                {
                    Console.WriteLine("⚠️  No configuration found, creating default configuration...");
                    _catalogConfig = CreateDefaultConfiguration();
                    await _pluginConfiguration.SaveConfigurationAsync(_catalogConfig);
                    Console.WriteLine("✅ Default configuration created and saved");
                }
                else
                {
                    Console.WriteLine("✅ Plugin configuration loaded successfully");
                    Console.WriteLine($"   Default Page Size: {_catalogConfig.Catalog.DefaultPageSize}");
                    Console.WriteLine($"   Default View: {_catalogConfig.Display.DefaultView}");
                    Console.WriteLine($"   Enable Export: {_catalogConfig.Export.EnableExport}");
                    Console.WriteLine($"   Enable Caching: {_catalogConfig.Performance.EnableCaching}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️  Error loading configuration: {ex.Message}");
                Console.WriteLine("   Using default configuration...");
                _catalogConfig = CreateDefaultConfiguration();
            }
        }

        private void ApplyConfigurationToMetadata(CatalogPluginConfiguration config)
        {
            // Update metadata from configuration
            if (config.Metadata != null)
            {
                // Update the base metadata with configuration values
                var metadata = new PluginMetadata
                {
                    Id = config.PluginId,
                    Name = config.Name,
                    Description = config.Description,
                    Version = config.Version,
                    Author = config.Author,
                    Category = config.Metadata.Category,
                    Icon = config.Metadata.Icon,
                    Type = Enum.TryParse<PluginType>(config.Metadata.Type, out var type) ? type : PluginType.Page,
                    RequiresAuthentication = config.Permissions.RequiresAuthentication,
                    RequiredRoles = config.Permissions.RequiredRoles,
                    RequiredPermissions = config.Permissions.RequiredPermissions
                };

                // Apply to the base plugin metadata
                _metadata = metadata;
            }
        }

        private CatalogPluginConfiguration CreateDefaultConfiguration()
        {
            return new CatalogPluginConfiguration
            {
                PluginId = "catalog-management",
                Name = "Catalog Management",
                Description = "Comprehensive catalog and product management system",
                Version = "1.0.0",
                Author = "LegendX Team",
                Enabled = true,
                LastModified = DateTime.UtcNow,
                Metadata = new PluginMetadataConfig
                {
                    Type = "Page",
                    Category = "Business",
                    Icon = "bi bi-grid-3x3-gap",
                    Route = "/catalog",
                    ComponentName = "CatalogManagement",
                    AssemblyName = "LegendXManager.Plugins.CatalogManagement",
                    Tags = new[] { "catalog", "products", "inventory", "business", "management" },
                    MinimumAppVersion = "1.0.0",
                    Dependencies = new[] { "LegendXManager.Shared >= 1.0.0" }
                },
                Permissions = new PluginPermissions
                {
                    RequiredRoles = new[] { "Admin", "Manager", "CatalogManager" },
                    RequiredPermissions = new[] { "CatalogManagement", "ProductManagement" },
                    RequiresAuthentication = true
                },
                UI = new PluginUI
                {
                    Theme = "dark",
                    ShowInNavigation = true,
                    NavigationOrder = 10,
                    NavigationIcon = "bi bi-grid-3x3-gap",
                    NavigationCategory = "Business"
                }
            };
        }

        /// <summary>
        /// Register plugin services with the DI container
        /// </summary>
        public static void ConfigureServices(IServiceCollection services)
        {
            // Register plugin-specific services
            services.AddScoped<ICatalogService, CatalogService>();
            services.AddScoped<IPluginConfiguration>(provider => 
                new PluginConfiguration("catalog-management"));
            
            Console.WriteLine("✅ Catalog Management Plugin services registered");
        }

        /// <summary>
        /// Get plugin configuration
        /// </summary>
        public CatalogPluginConfiguration? GetConfiguration()
        {
            return _catalogConfig;
        }

        /// <summary>
        /// Update plugin configuration
        /// </summary>
        public async Task UpdateConfigurationAsync(CatalogPluginConfiguration configuration)
        {
            _catalogConfig = configuration;
            await _pluginConfiguration.SaveConfigurationAsync(configuration);
            ApplyConfigurationToMetadata(configuration);
            Console.WriteLine("✅ Plugin configuration updated");
        }

        /// <summary>
        /// Get plugin information for display
        /// </summary>
        public static PluginInfo GetPluginInfo()
        {
            return new PluginInfo
            {
                Id = "catalog-management",
                Name = "Catalog Management",
                Description = "Comprehensive catalog and product management system",
                Version = "1.0.0",
                Author = "LegendX Team",
                Category = "Business",
                Icon = "bi bi-grid-3x3-gap",
                Features = new[]
                {
                    "Product catalog management",
                    "Advanced search and filtering",
                    "Category management",
                    "Stock tracking",
                    "Price management",
                    "Product analytics",
                    "Bulk operations",
                    "Export/Import capabilities",
                    "Configurable UI and behavior",
                    "Role-based permissions"
                },
                Screenshots = new[]
                {
                    "/images/plugins/catalog-grid-view.png",
                    "/images/plugins/catalog-list-view.png",
                    "/images/plugins/catalog-analytics.png"
                },
                Documentation = "/docs/plugins/catalog-management",
                SupportUrl = "https://support.legendx.com/catalog-management",
                MinimumVersion = "1.0.0",
                Dependencies = new[]
                {
                    "LegendXManager.Shared >= 1.0.0"
                }
            };
        }
    }

    public class PluginInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string[] Features { get; set; } = Array.Empty<string>();
        public string[] Screenshots { get; set; } = Array.Empty<string>();
        public string Documentation { get; set; } = string.Empty;
        public string SupportUrl { get; set; } = string.Empty;
        public string MinimumVersion { get; set; } = string.Empty;
        public string[] Dependencies { get; set; } = Array.Empty<string>();
    }
}

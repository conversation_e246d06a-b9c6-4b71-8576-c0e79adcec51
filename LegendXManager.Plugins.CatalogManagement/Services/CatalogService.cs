using LegendXManager.Plugins.CatalogManagement.Models;

namespace LegendXManager.Plugins.CatalogManagement.Services
{
    public class CatalogService : ICatalogService
    {
        private readonly List<Catalog> _catalogs;
        private readonly List<CatalogCategory> _categories;

        public CatalogService()
        {
            _categories = new List<CatalogCategory>
            {
                new() { Id = 1, Name = "Electronics", Description = "Electronic devices and accessories", Icon = "bi bi-laptop", Color = "#007acc", SortOrder = 1 },
                new() { Id = 2, Name = "Books", Description = "Books and publications", Icon = "bi bi-book", Color = "#28a745", SortOrder = 2 },
                new() { Id = 3, Name = "Clothing", Description = "Apparel and fashion items", Icon = "bi bi-bag", Color = "#dc3545", SortOrder = 3 },
                new() { Id = 4, Name = "Home & Garden", Description = "Home improvement and garden supplies", Icon = "bi bi-house", Color = "#fd7e14", SortOrder = 4 },
                new() { Id = 5, Name = "Sports", Description = "Sports equipment and accessories", Icon = "bi bi-trophy", Color = "#6f42c1", SortOrder = 5 },
                new() { Id = 6, Name = "Automotive", Description = "Car parts and accessories", Icon = "bi bi-car-front", Color = "#20c997", SortOrder = 6 }
            };

            _catalogs = new List<Catalog>
            {
                new() { Id = 1, Name = "MacBook Pro 16\"", Description = "Apple MacBook Pro with M3 chip, 16GB RAM, 512GB SSD", Category = "Electronics", Price = 2499.99m, CostPrice = 2000m, SKU = "MBP16-M3-001", StockQuantity = 15, Tags = "laptop,apple,macbook,professional", Supplier = "Apple Inc.", Weight = 2.1, IsFeatured = true, AverageRating = 4.8, ReviewCount = 127 },
                new() { Id = 2, Name = "Wireless Gaming Mouse", Description = "High-precision wireless gaming mouse with RGB lighting", Category = "Electronics", Price = 79.99m, CostPrice = 45m, SKU = "WGM-RGB-002", StockQuantity = 85, Tags = "mouse,gaming,wireless,rgb", Supplier = "TechGear Co.", Weight = 0.12, IsFeatured = false, AverageRating = 4.5, ReviewCount = 89 },
                new() { Id = 3, Name = "Clean Code", Description = "A Handbook of Agile Software Craftsmanship by Robert C. Martin", Category = "Books", Price = 42.99m, CostPrice = 25m, SKU = "BOOK-CC-003", StockQuantity = 45, Tags = "book,programming,clean code,software", Supplier = "Pearson Education", Weight = 0.68, IsFeatured = true, AverageRating = 4.7, ReviewCount = 234 },
                new() { Id = 4, Name = "Premium Cotton T-Shirt", Description = "100% organic cotton t-shirt, available in multiple colors", Category = "Clothing", Price = 29.99m, CostPrice = 12m, SKU = "TSHIRT-COT-004", StockQuantity = 120, Tags = "shirt,cotton,organic,casual", Supplier = "EcoWear Ltd.", Weight = 0.18, IsFeatured = false, AverageRating = 4.3, ReviewCount = 67 },
                new() { Id = 5, Name = "Professional Garden Tool Set", Description = "Complete 10-piece garden tool set with carrying case", Category = "Home & Garden", Price = 149.99m, CostPrice = 85m, SKU = "GARDEN-SET-005", StockQuantity = 28, Tags = "tools,garden,professional,set", Supplier = "GreenThumb Tools", Weight = 3.2, IsFeatured = true, AverageRating = 4.6, ReviewCount = 156 },
                new() { Id = 6, Name = "Professional Basketball", Description = "Official size and weight basketball for professional play", Category = "Sports", Price = 34.99m, CostPrice = 18m, SKU = "BALL-BB-006", StockQuantity = 75, Tags = "basketball,sports,professional,official", Supplier = "SportsPro Inc.", Weight = 0.62, IsFeatured = false, AverageRating = 4.4, ReviewCount = 92 },
                new() { Id = 7, Name = "iPhone 15 Pro Case", Description = "Premium leather case for iPhone 15 Pro with MagSafe support", Category = "Electronics", Price = 59.99m, CostPrice = 25m, SKU = "CASE-IP15-007", StockQuantity = 200, Tags = "case,iphone,leather,magsafe", Supplier = "CaseCraft", Weight = 0.08, IsFeatured = false, AverageRating = 4.2, ReviewCount = 78 },
                new() { Id = 8, Name = "Mediterranean Cookbook", Description = "Authentic Mediterranean recipes for healthy living", Category = "Books", Price = 28.99m, CostPrice = 15m, SKU = "BOOK-MED-008", StockQuantity = 35, Tags = "book,cooking,mediterranean,healthy", Supplier = "Culinary Press", Weight = 0.45, IsFeatured = false, AverageRating = 4.5, ReviewCount = 43 },
                new() { Id = 9, Name = "Ultra Running Shoes", Description = "Lightweight running shoes with advanced cushioning technology", Category = "Sports", Price = 159.99m, CostPrice = 80m, SKU = "SHOES-RUN-009", StockQuantity = 42, Tags = "shoes,running,lightweight,cushioning", Supplier = "RunTech", Weight = 0.28, IsFeatured = true, AverageRating = 4.7, ReviewCount = 189 },
                new() { Id = 10, Name = "Smart LED Desk Lamp", Description = "WiFi-enabled LED desk lamp with app control and scheduling", Category = "Home & Garden", Price = 89.99m, CostPrice = 45m, SKU = "LAMP-LED-010", StockQuantity = 65, Tags = "lamp,led,smart,wifi,desk", Supplier = "SmartHome Tech", Weight = 1.2, IsFeatured = false, AverageRating = 4.3, ReviewCount = 112 },
                new() { Id = 11, Name = "Wireless Earbuds Pro", Description = "Premium wireless earbuds with active noise cancellation", Category = "Electronics", Price = 199.99m, CostPrice = 120m, SKU = "EARBUDS-PRO-011", StockQuantity = 8, Tags = "earbuds,wireless,noise cancellation,premium", Supplier = "AudioTech", Weight = 0.05, IsFeatured = true, AverageRating = 4.6, ReviewCount = 267 },
                new() { Id = 12, Name = "Car Phone Mount", Description = "Universal magnetic car phone mount with 360° rotation", Category = "Automotive", Price = 24.99m, CostPrice = 8m, SKU = "MOUNT-CAR-012", StockQuantity = 150, Tags = "mount,car,phone,magnetic,universal", Supplier = "AutoAccessories", Weight = 0.15, IsFeatured = false, AverageRating = 4.1, ReviewCount = 56 }
            };

            // Update category product counts
            foreach (var category in _categories)
            {
                category.ProductCount = _catalogs.Count(c => c.Category == category.Name && c.IsActive);
            }
        }

        public Task<List<Catalog>> GetCatalogsAsync(CatalogFilter? filter = null)
        {
            var query = _catalogs.Where(c => c.IsActive).AsEnumerable();

            if (filter != null)
            {
                if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
                {
                    query = query.Where(c => 
                        c.Name.Contains(filter.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                        c.Description.Contains(filter.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                        c.Tags.Contains(filter.SearchTerm, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrWhiteSpace(filter.Category))
                {
                    query = query.Where(c => c.Category.Equals(filter.Category, StringComparison.OrdinalIgnoreCase));
                }

                if (filter.MinPrice.HasValue)
                {
                    query = query.Where(c => c.Price >= filter.MinPrice.Value);
                }

                if (filter.MaxPrice.HasValue)
                {
                    query = query.Where(c => c.Price <= filter.MaxPrice.Value);
                }

                if (filter.InStock.HasValue)
                {
                    query = filter.InStock.Value ? query.Where(c => c.StockQuantity > 0) : query.Where(c => c.StockQuantity == 0);
                }

                if (filter.IsFeatured.HasValue)
                {
                    query = query.Where(c => c.IsFeatured == filter.IsFeatured.Value);
                }

                // Apply sorting
                query = filter.SortBy.ToLower() switch
                {
                    "price" => filter.SortDirection == "desc" ? query.OrderByDescending(c => c.Price) : query.OrderBy(c => c.Price),
                    "category" => filter.SortDirection == "desc" ? query.OrderByDescending(c => c.Category) : query.OrderBy(c => c.Category),
                    "stock" => filter.SortDirection == "desc" ? query.OrderByDescending(c => c.StockQuantity) : query.OrderBy(c => c.StockQuantity),
                    "rating" => filter.SortDirection == "desc" ? query.OrderByDescending(c => c.AverageRating) : query.OrderBy(c => c.AverageRating),
                    "created" => filter.SortDirection == "desc" ? query.OrderByDescending(c => c.CreatedDate) : query.OrderBy(c => c.CreatedDate),
                    _ => filter.SortDirection == "desc" ? query.OrderByDescending(c => c.Name) : query.OrderBy(c => c.Name)
                };
            }
            else
            {
                query = query.OrderBy(c => c.Name);
            }

            return Task.FromResult(query.ToList());
        }

        public Task<List<Catalog>> GetCatalogsByCategoryAsync(string category)
        {
            return Task.FromResult(_catalogs.Where(c => c.IsActive && c.Category.Equals(category, StringComparison.OrdinalIgnoreCase)).ToList());
        }

        public Task<Catalog?> GetCatalogByIdAsync(int id)
        {
            var catalog = _catalogs.FirstOrDefault(c => c.Id == id && c.IsActive);
            if (catalog != null)
            {
                catalog.ViewCount++;
            }
            return Task.FromResult(catalog);
        }

        public Task<Catalog> CreateCatalogAsync(Catalog catalog)
        {
            catalog.Id = _catalogs.Max(c => c.Id) + 1;
            catalog.CreatedDate = DateTime.Now;
            catalog.LastModified = DateTime.Now;
            _catalogs.Add(catalog);
            return Task.FromResult(catalog);
        }

        public Task<Catalog> UpdateCatalogAsync(Catalog catalog)
        {
            var existing = _catalogs.FirstOrDefault(c => c.Id == catalog.Id);
            if (existing != null)
            {
                existing.Name = catalog.Name;
                existing.Description = catalog.Description;
                existing.Category = catalog.Category;
                existing.Price = catalog.Price;
                existing.CostPrice = catalog.CostPrice;
                existing.ImageUrl = catalog.ImageUrl;
                existing.IsActive = catalog.IsActive;
                existing.LastModified = DateTime.Now;
                existing.Tags = catalog.Tags;
                existing.StockQuantity = catalog.StockQuantity;
                existing.SKU = catalog.SKU;
                existing.Supplier = catalog.Supplier;
                existing.Weight = catalog.Weight;
                existing.Dimensions = catalog.Dimensions;
                existing.IsFeatured = catalog.IsFeatured;
                existing.Barcode = catalog.Barcode;
                return Task.FromResult(existing);
            }
            throw new ArgumentException("Catalog not found");
        }

        public Task<bool> DeleteCatalogAsync(int id)
        {
            var catalog = _catalogs.FirstOrDefault(c => c.Id == id);
            if (catalog != null)
            {
                catalog.IsActive = false;
                catalog.LastModified = DateTime.Now;
                return Task.FromResult(true);
            }
            return Task.FromResult(false);
        }

        public Task<List<CatalogCategory>> GetCategoriesAsync()
        {
            return Task.FromResult(_categories.Where(c => c.IsActive).OrderBy(c => c.SortOrder).ToList());
        }

        public Task<List<string>> SearchCatalogsAsync(string searchTerm)
        {
            var results = _catalogs
                .Where(c => c.IsActive && (
                    c.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    c.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    c.Tags.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                ))
                .Select(c => c.Name)
                .Take(10)
                .ToList();
            
            return Task.FromResult(results);
        }

        public async Task<(List<Catalog> Items, int TotalCount)> GetPagedCatalogsAsync(CatalogFilter filter)
        {
            var allItems = await GetCatalogsAsync(filter);
            var totalCount = allItems.Count;
            
            var pagedItems = allItems
                .Skip((filter.PageNumber - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToList();

            return (pagedItems, totalCount);
        }

        public Task<Dictionary<string, int>> GetCatalogStatisticsAsync()
        {
            var stats = new Dictionary<string, int>
            {
                ["TotalProducts"] = _catalogs.Count(c => c.IsActive),
                ["InStock"] = _catalogs.Count(c => c.IsActive && c.StockQuantity > 0),
                ["OutOfStock"] = _catalogs.Count(c => c.IsActive && c.StockQuantity == 0),
                ["LowStock"] = _catalogs.Count(c => c.IsActive && c.StockQuantity > 0 && c.StockQuantity <= 10),
                ["Featured"] = _catalogs.Count(c => c.IsActive && c.IsFeatured),
                ["Categories"] = _categories.Count(c => c.IsActive)
            };

            return Task.FromResult(stats);
        }

        public Task<bool> UpdateStockAsync(int catalogId, int newStock)
        {
            var catalog = _catalogs.FirstOrDefault(c => c.Id == catalogId && c.IsActive);
            if (catalog != null)
            {
                catalog.StockQuantity = newStock;
                catalog.LastModified = DateTime.Now;
                return Task.FromResult(true);
            }
            return Task.FromResult(false);
        }

        public Task<List<Catalog>> GetFeaturedCatalogsAsync(int count = 10)
        {
            return Task.FromResult(_catalogs
                .Where(c => c.IsActive && c.IsFeatured)
                .OrderByDescending(c => c.AverageRating)
                .Take(count)
                .ToList());
        }

        public Task<List<Catalog>> GetLowStockCatalogsAsync(int threshold = 10)
        {
            return Task.FromResult(_catalogs
                .Where(c => c.IsActive && c.StockQuantity > 0 && c.StockQuantity <= threshold)
                .OrderBy(c => c.StockQuantity)
                .ToList());
        }
    }
}

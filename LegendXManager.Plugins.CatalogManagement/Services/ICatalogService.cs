using LegendXManager.Plugins.CatalogManagement.Models;

namespace LegendXManager.Plugins.CatalogManagement.Services
{
    public interface ICatalogService
    {
        Task<List<Catalog>> GetCatalogsAsync(CatalogFilter? filter = null);
        Task<List<Catalog>> GetCatalogsByCategoryAsync(string category);
        Task<Catalog?> GetCatalogByIdAsync(int id);
        Task<Catalog> CreateCatalogAsync(Catalog catalog);
        Task<Catalog> UpdateCatalogAsync(Catalog catalog);
        Task<bool> DeleteCatalogAsync(int id);
        Task<List<CatalogCategory>> GetCategoriesAsync();
        Task<List<string>> SearchCatalogsAsync(string searchTerm);
        Task<(List<Catalog> Items, int TotalCount)> GetPagedCatalogsAsync(CatalogFilter filter);
        Task<Dictionary<string, int>> GetCatalogStatisticsAsync();
        Task<bool> UpdateStockAsync(int catalogId, int newStock);
        Task<List<Catalog>> GetFeaturedCatalogsAsync(int count = 10);
        Task<List<Catalog>> GetLowStockCatalogsAsync(int threshold = 10);
    }
}

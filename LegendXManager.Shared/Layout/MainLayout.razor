@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.JSInterop
@inherits LayoutComponentBase
@implements IDisposable
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<div class="ide-container">
    <div class="ide-header">
        <div class="ide-toolbar">
            <div class="toolbar-section">
                <span class="app-title">LegendX Manager</span>
            </div>
            <div class="toolbar-section">
                <a href="https://learn.microsoft.com/aspnet/core/" target="_blank" class="btn btn-sm btn-outline-light">
                    <i class="bi bi-info-circle"></i> About
                </a>
            </div>
        </div>
    </div>

    <div class="ide-body">
        <div class="ide-sidebar">
            <NavMenu OnNavigate="HandleNavigation"/>
        </div>

        <div class="ide-main">
            <div class="editor-container">
                <div class="editor-tabs">
                    @if (openTabs.Any())
                    {
                        @foreach (var tab in openTabs)
                        {
                            <div class="editor-tab @(tab.IsActive ? "active" : "")" @onclick="() => SetActiveTab(tab)">
                                <i class="@GetTabIcon(tab.Route) nav-icon"></i>
                                <span class="tab-title">@tab.Title</span>
                                @if (openTabs.Count > 1)
                                {
                                    <button class="tab-close" @onclick="() => CloseTab(tab)" @onclick:stopPropagation="true">
                                        <i class="bi bi-x"></i>
                                    </button>
                                }
                            </div>
                        }
                    }
                    else
                    {
                        <div class="no-tabs">No pages open</div>
                    }
                </div>

                <div class="editor-content">
                    @if (activeTab != null)
                    {
                        <article class="content">
                            @Body
                        </article>
                    }
                    else
                    {
                        <div class="welcome-screen">
                            <div class="welcome-content">
                                <h3>Welcome to LegendX Manager</h3>
                                <p>Select a page from the navigation to get started.</p>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="ide-footer">
        <div class="status-bar">
            <div class="status-left">
                <div class="status-item">
                    <i class="bi bi-house"></i>
                    <span>LegendX Manager</span>
                </div>
            </div>
            <div class="status-center">
                <div class="status-item">
                    <span>@(activeTab?.Title ?? "Ready")</span>
                </div>
            </div>
            <div class="status-right">
                <div class="status-item">
                    <span>@DateTime.Now.ToString("HH:mm")</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

@code {
    private List<TabInfo> openTabs = new();
    private TabInfo? activeTab;

    public class TabInfo
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Title { get; set; } = "";
        public string Route { get; set; } = "";
        public bool IsActive { get; set; }
    }

    protected override void OnInitialized()
    {
        Navigation.LocationChanged += OnLocationChanged;
        
        // Initialize with current page
        var currentRoute = GetCurrentRoute();
        OpenTab(currentRoute, GetPageTitle(currentRoute));
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        var route = GetRouteFromUrl(e.Location);
        var title = GetPageTitle(route);
        OpenTab(route, title);
        InvokeAsync(StateHasChanged);
    }

    private string GetCurrentRoute()
    {
        var uri = Navigation.ToBaseRelativePath(Navigation.Uri);
        return "/" + uri.Split('?')[0].TrimStart('/');
    }

    private string GetRouteFromUrl(string url)
    {
        var uri = Navigation.ToBaseRelativePath(url);
        return "/" + uri.Split('?')[0].TrimStart('/');
    }

    private string GetPageTitle(string route)
    {
        return route switch
        {
            "/" => "Home",
            "/counter" => "Counter",
            "/weather" => "Weather",
            "/ide" => "IDE",
            "/profile" => "Profile",
            "/admin/users" => "User Management",
            "/login" => "Login",
            "/register" => "Register",
            _ => "Page"
        };
    }

    private string GetTabIcon(string route)
    {
        return route switch
        {
            "/" => "bi bi-house-door-fill",
            "/counter" => "bi bi-plus-square-fill",
            "/weather" => "bi bi-list-nested",
            "/ide" => "bi bi-code-square",
            "/profile" => "bi bi-person",
            "/admin/users" => "bi bi-people",
            "/login" => "bi bi-box-arrow-in-right",
            "/register" => "bi bi-person-plus",
            _ => "bi bi-file-earmark"
        };
    }

    private void OpenTab(string route, string title)
    {
        // Check if tab already exists
        var existingTab = openTabs.FirstOrDefault(t => t.Route == route);
        if (existingTab != null)
        {
            SetActiveTab(existingTab);
            return;
        }

        // Create new tab
        var newTab = new TabInfo
        {
            Title = title,
            Route = route,
            IsActive = true
        };

        // Deactivate other tabs
        foreach (var tab in openTabs)
            tab.IsActive = false;

        openTabs.Add(newTab);
        activeTab = newTab;
    }

    private void SetActiveTab(TabInfo tab)
    {
        foreach (var t in openTabs)
            t.IsActive = false;
        
        tab.IsActive = true;
        activeTab = tab;
        
        // Navigate to the tab's route
        Navigation.NavigateTo(tab.Route);
    }

    private void CloseTab(TabInfo tab)
    {
        openTabs.Remove(tab);
        
        if (tab == activeTab)
        {
            activeTab = openTabs.LastOrDefault();
            if (activeTab != null)
            {
                activeTab.IsActive = true;
                Navigation.NavigateTo(activeTab.Route);
            }
            else
            {
                // If no tabs left, navigate to home
                Navigation.NavigateTo("/");
            }
        }
    }

    private void HandleNavigation(string route)
    {
        // This will be called from NavMenu when a navigation item is clicked
        // The navigation will trigger OnLocationChanged which will handle tab creation
    }

    public void Dispose()
    {
        Navigation.LocationChanged -= OnLocationChanged;
    }
}

using LegendXManager.Shared.Models;

namespace LegendXManager.Shared.Services
{
    public class CatalogService : ICatalogService
    {
        private readonly List<Catalog> _catalogs;
        private readonly List<CatalogCategory> _categories;

        public CatalogService()
        {
            _categories = new List<CatalogCategory>
            {
                new() { Id = 1, Name = "Electronics", Description = "Electronic devices and accessories", Icon = "bi bi-laptop" },
                new() { Id = 2, Name = "Books", Description = "Books and publications", Icon = "bi bi-book" },
                new() { Id = 3, Name = "Clothing", Description = "Apparel and fashion items", Icon = "bi bi-bag" },
                new() { Id = 4, Name = "Home & Garden", Description = "Home improvement and garden supplies", Icon = "bi bi-house" },
                new() { Id = 5, Name = "Sports", Description = "Sports equipment and accessories", Icon = "bi bi-trophy" }
            };

            _catalogs = new List<Catalog>
            {
                new() { Id = 1, Name = "Laptop Pro 15", Description = "High-performance laptop for professionals", Category = "Electronics", Price = 1299.99m, SKU = "LP15-001", StockQuantity = 25, Tags = "laptop,computer,professional" },
                new() { Id = 2, Name = "Wireless Mouse", Description = "Ergonomic wireless mouse", Category = "Electronics", Price = 29.99m, SKU = "WM-002", StockQuantity = 150, Tags = "mouse,wireless,ergonomic" },
                new() { Id = 3, Name = "Programming Guide", Description = "Complete guide to modern programming", Category = "Books", Price = 49.99m, SKU = "PG-003", StockQuantity = 75, Tags = "book,programming,guide" },
                new() { Id = 4, Name = "Cotton T-Shirt", Description = "Comfortable cotton t-shirt", Category = "Clothing", Price = 19.99m, SKU = "CT-004", StockQuantity = 200, Tags = "shirt,cotton,comfortable" },
                new() { Id = 5, Name = "Garden Tools Set", Description = "Complete set of garden tools", Category = "Home & Garden", Price = 89.99m, SKU = "GT-005", StockQuantity = 50, Tags = "tools,garden,set" },
                new() { Id = 6, Name = "Basketball", Description = "Professional basketball", Category = "Sports", Price = 24.99m, SKU = "BB-006", StockQuantity = 100, Tags = "basketball,sports,professional" },
                new() { Id = 7, Name = "Smartphone Case", Description = "Protective smartphone case", Category = "Electronics", Price = 15.99m, SKU = "SC-007", StockQuantity = 300, Tags = "case,phone,protection" },
                new() { Id = 8, Name = "Cookbook", Description = "Delicious recipes for home cooking", Category = "Books", Price = 34.99m, SKU = "CB-008", StockQuantity = 60, Tags = "book,cooking,recipes" },
                new() { Id = 9, Name = "Running Shoes", Description = "Lightweight running shoes", Category = "Sports", Price = 79.99m, SKU = "RS-009", StockQuantity = 80, Tags = "shoes,running,lightweight" },
                new() { Id = 10, Name = "LED Desk Lamp", Description = "Adjustable LED desk lamp", Category = "Home & Garden", Price = 39.99m, SKU = "DL-010", StockQuantity = 120, Tags = "lamp,led,desk,adjustable" }
            };
        }

        public Task<List<Catalog>> GetCatalogsAsync()
        {
            return Task.FromResult(_catalogs.Where(c => c.IsActive).ToList());
        }

        public Task<List<Catalog>> GetCatalogsByCategoryAsync(string category)
        {
            return Task.FromResult(_catalogs.Where(c => c.IsActive && c.Category.Equals(category, StringComparison.OrdinalIgnoreCase)).ToList());
        }

        public Task<Catalog?> GetCatalogByIdAsync(int id)
        {
            return Task.FromResult(_catalogs.FirstOrDefault(c => c.Id == id && c.IsActive));
        }

        public Task<Catalog> CreateCatalogAsync(Catalog catalog)
        {
            catalog.Id = _catalogs.Max(c => c.Id) + 1;
            catalog.CreatedDate = DateTime.Now;
            catalog.LastModified = DateTime.Now;
            _catalogs.Add(catalog);
            return Task.FromResult(catalog);
        }

        public Task<Catalog> UpdateCatalogAsync(Catalog catalog)
        {
            var existing = _catalogs.FirstOrDefault(c => c.Id == catalog.Id);
            if (existing != null)
            {
                existing.Name = catalog.Name;
                existing.Description = catalog.Description;
                existing.Category = catalog.Category;
                existing.Price = catalog.Price;
                existing.ImageUrl = catalog.ImageUrl;
                existing.IsActive = catalog.IsActive;
                existing.LastModified = DateTime.Now;
                existing.Tags = catalog.Tags;
                existing.StockQuantity = catalog.StockQuantity;
                existing.SKU = catalog.SKU;
                return Task.FromResult(existing);
            }
            throw new ArgumentException("Catalog not found");
        }

        public Task<bool> DeleteCatalogAsync(int id)
        {
            var catalog = _catalogs.FirstOrDefault(c => c.Id == id);
            if (catalog != null)
            {
                catalog.IsActive = false;
                catalog.LastModified = DateTime.Now;
                return Task.FromResult(true);
            }
            return Task.FromResult(false);
        }

        public Task<List<CatalogCategory>> GetCategoriesAsync()
        {
            return Task.FromResult(_categories.Where(c => c.IsActive).ToList());
        }

        public Task<List<string>> SearchCatalogsAsync(string searchTerm)
        {
            var results = _catalogs
                .Where(c => c.IsActive && (
                    c.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    c.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    c.Tags.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                ))
                .Select(c => c.Name)
                .ToList();
            
            return Task.FromResult(results);
        }
    }
}

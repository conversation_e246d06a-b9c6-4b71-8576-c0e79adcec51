using LegendXManager.Shared.Models;

namespace LegendXManager.Shared.Services
{
    public interface ICatalogService
    {
        Task<List<Catalog>> GetCatalogsAsync();
        Task<List<Catalog>> GetCatalogsByCategoryAsync(string category);
        Task<Catalog?> GetCatalogByIdAsync(int id);
        Task<Catalog> CreateCatalogAsync(Catalog catalog);
        Task<Catalog> UpdateCatalogAsync(Catalog catalog);
        Task<bool> DeleteCatalogAsync(int id);
        Task<List<CatalogCategory>> GetCategoriesAsync();
        Task<List<string>> SearchCatalogsAsync(string searchTerm);
    }
}

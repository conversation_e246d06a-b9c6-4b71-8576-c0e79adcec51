using System.Text.Json;
using System.Reflection;

namespace LegendXManager.Shared.Services.Plugins
{
    public interface IPluginConfigurationLoader
    {
        Task<PluginConfigurationBase?> LoadConfigurationAsync(string pluginPath);
        Task<PluginConfigurationBase?> LoadConfigurationFromJsonAsync(string jsonContent);
        Task<List<PluginConfigurationBase>> LoadAllConfigurationsAsync(string pluginsDirectory);
        Task<bool> ValidateConfigurationAsync(PluginConfigurationBase configuration);
        Task SaveConfigurationAsync(PluginConfigurationBase configuration, string pluginPath);
    }

    public class PluginConfigurationLoader : IPluginConfigurationLoader
    {
        private readonly JsonSerializerOptions _jsonOptions;

        public PluginConfigurationLoader()
        {
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                WriteIndented = true,
                AllowTrailingCommas = true,
                ReadCommentHandling = JsonCommentHandling.Skip
            };
        }

        public async Task<PluginConfigurationBase?> LoadConfigurationAsync(string pluginPath)
        {
            try
            {
                var configPath = Path.Combine(pluginPath, "plugin.config.json");
                
                if (!File.Exists(configPath))
                {
                    Console.WriteLine($"Plugin configuration not found: {configPath}");
                    return null;
                }

                var jsonContent = await File.ReadAllTextAsync(configPath);
                return await LoadConfigurationFromJsonAsync(jsonContent);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading plugin configuration from {pluginPath}: {ex.Message}");
                return null;
            }
        }

        public async Task<PluginConfigurationBase?> LoadConfigurationFromJsonAsync(string jsonContent)
        {
            try
            {
                var configuration = JsonSerializer.Deserialize<PluginConfigurationBase>(jsonContent, _jsonOptions);
                
                if (configuration != null)
                {
                    // Validate required fields
                    if (string.IsNullOrWhiteSpace(configuration.PluginId))
                    {
                        throw new InvalidOperationException("Plugin configuration must have a valid PluginId");
                    }

                    if (string.IsNullOrWhiteSpace(configuration.Name))
                    {
                        configuration.Name = configuration.PluginId;
                    }

                    // Set defaults if not specified
                    configuration.Metadata ??= new PluginMetadataConfig();
                    configuration.Permissions ??= new PluginPermissions();
                    configuration.UI ??= new PluginUI();
                    configuration.Features ??= new PluginFeatures();

                    // Update last modified if not set
                    if (configuration.LastModified == default)
                    {
                        configuration.LastModified = DateTime.UtcNow;
                    }

                    Console.WriteLine($"✓ Loaded configuration for plugin: {configuration.Name} v{configuration.Version}");
                    Console.WriteLine($"  Type: {configuration.Metadata.Type}");
                    Console.WriteLine($"  Category: {configuration.Metadata.Category}");
                    Console.WriteLine($"  Icon: {configuration.Metadata.Icon}");
                    Console.WriteLine($"  Route: {configuration.Metadata.Route}");
                }

                return configuration;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing plugin configuration JSON: {ex.Message}");
                return null;
            }
        }

        public async Task<List<PluginConfigurationBase>> LoadAllConfigurationsAsync(string pluginsDirectory)
        {
            var configurations = new List<PluginConfigurationBase>();

            try
            {
                if (!Directory.Exists(pluginsDirectory))
                {
                    Console.WriteLine($"Plugins directory not found: {pluginsDirectory}");
                    return configurations;
                }

                var pluginDirectories = Directory.GetDirectories(pluginsDirectory);
                
                foreach (var pluginDir in pluginDirectories)
                {
                    var config = await LoadConfigurationAsync(pluginDir);
                    if (config != null && await ValidateConfigurationAsync(config))
                    {
                        configurations.Add(config);
                    }
                }

                Console.WriteLine($"Loaded {configurations.Count} plugin configurations from {pluginsDirectory}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading plugin configurations: {ex.Message}");
            }

            return configurations;
        }

        public Task<bool> ValidateConfigurationAsync(PluginConfigurationBase configuration)
        {
            try
            {
                // Required field validation
                if (string.IsNullOrWhiteSpace(configuration.PluginId))
                {
                    Console.WriteLine("❌ Plugin configuration validation failed: PluginId is required");
                    return Task.FromResult(false);
                }

                if (string.IsNullOrWhiteSpace(configuration.Name))
                {
                    Console.WriteLine("❌ Plugin configuration validation failed: Name is required");
                    return Task.FromResult(false);
                }

                // Version validation
                if (!Version.TryParse(configuration.Version, out _))
                {
                    Console.WriteLine($"❌ Plugin configuration validation failed: Invalid version format '{configuration.Version}'");
                    return Task.FromResult(false);
                }

                // Metadata validation
                if (configuration.Metadata != null)
                {
                    var validTypes = new[] { "Page", "Tool", "Widget", "Service" };
                    if (!validTypes.Contains(configuration.Metadata.Type))
                    {
                        Console.WriteLine($"❌ Plugin configuration validation failed: Invalid type '{configuration.Metadata.Type}'. Must be one of: {string.Join(", ", validTypes)}");
                        return Task.FromResult(false);
                    }

                    if (string.IsNullOrWhiteSpace(configuration.Metadata.Category))
                    {
                        Console.WriteLine("❌ Plugin configuration validation failed: Category is required");
                        return Task.FromResult(false);
                    }

                    if (string.IsNullOrWhiteSpace(configuration.Metadata.Icon))
                    {
                        Console.WriteLine("❌ Plugin configuration validation failed: Icon is required");
                        return Task.FromResult(false);
                    }
                }

                // Dependencies validation
                if (configuration.Metadata?.Dependencies != null)
                {
                    foreach (var dependency in configuration.Metadata.Dependencies)
                    {
                        if (string.IsNullOrWhiteSpace(dependency))
                        {
                            Console.WriteLine("❌ Plugin configuration validation failed: Empty dependency found");
                            return Task.FromResult(false);
                        }
                    }
                }

                Console.WriteLine($"✓ Plugin configuration validation passed for: {configuration.Name}");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Plugin configuration validation error: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        public async Task SaveConfigurationAsync(PluginConfigurationBase configuration, string pluginPath)
        {
            try
            {
                var configPath = Path.Combine(pluginPath, "plugin.config.json");
                
                // Update last modified timestamp
                configuration.LastModified = DateTime.UtcNow;

                var json = JsonSerializer.Serialize(configuration, _jsonOptions);
                await File.WriteAllTextAsync(configPath, json);

                Console.WriteLine($"✓ Saved plugin configuration: {configPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error saving plugin configuration: {ex.Message}");
                throw;
            }
        }
    }

    public static class PluginConfigurationExtensions
    {
        public static PluginMetadata ToPluginMetadata(this PluginConfigurationBase config)
        {
            return new PluginMetadata
            {
                Id = config.PluginId,
                Name = config.Name,
                Description = config.Description,
                Version = config.Version,
                Author = config.Author,
                Category = config.Metadata?.Category ?? "General",
                Icon = config.Metadata?.Icon ?? "bi bi-puzzle",
                Type = Enum.TryParse<PluginType>(config.Metadata?.Type ?? "Page", out var type) ? type : PluginType.Page,
                RequiresAuthentication = config.Permissions?.RequiresAuthentication ?? true,
                RequiredRoles = config.Permissions?.RequiredRoles ?? Array.Empty<string>(),
                RequiredPermissions = config.Permissions?.RequiredPermissions ?? Array.Empty<string>()
            };
        }

        public static bool IsEnabled(this PluginConfigurationBase config)
        {
            return config.Enabled && (config.Metadata?.Lifecycle?.AutoStart ?? true);
        }

        public static bool CanShowInNavigation(this PluginConfigurationBase config)
        {
            return config.UI?.ShowInNavigation ?? true;
        }

        public static string GetNavigationCategory(this PluginConfigurationBase config)
        {
            return config.UI?.NavigationCategory ?? config.Metadata?.Category ?? "General";
        }

        public static string GetNavigationIcon(this PluginConfigurationBase config)
        {
            return config.UI?.NavigationIcon ?? config.Metadata?.Icon ?? "bi bi-puzzle";
        }

        public static int GetNavigationOrder(this PluginConfigurationBase config)
        {
            return config.UI?.NavigationOrder ?? 100;
        }
    }
}

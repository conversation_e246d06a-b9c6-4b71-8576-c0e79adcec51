using System.Text.Json;

namespace LegendXManager.Shared.Services.Plugins
{
    public class PluginConfiguration : IPluginConfiguration
    {
        private readonly string _pluginId;
        private readonly string _configDirectory;

        public PluginConfiguration(string pluginId, string? configDirectory = null)
        {
            _pluginId = pluginId;
            _configDirectory = configDirectory ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PluginConfigs");
            
            // Ensure config directory exists
            Directory.CreateDirectory(_configDirectory);
        }

        public string ConfigurationPath => Path.Combine(_configDirectory, $"{_pluginId}.json");

        public async Task<T?> GetConfigurationAsync<T>() where T : class
        {
            try
            {
                if (!await ConfigurationExistsAsync())
                {
                    return null;
                }

                var json = await File.ReadAllTextAsync(ConfigurationPath);
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    WriteIndented = true
                };

                return JsonSerializer.Deserialize<T>(json, options);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading plugin configuration for {_pluginId}: {ex.Message}");
                return null;
            }
        }

        public async Task SaveConfigurationAsync<T>(T configuration) where T : class
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    WriteIndented = true
                };

                var json = JsonSerializer.Serialize(configuration, options);
                await File.WriteAllTextAsync(ConfigurationPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving plugin configuration for {_pluginId}: {ex.Message}");
                throw;
            }
        }

        public Task<bool> ConfigurationExistsAsync()
        {
            return Task.FromResult(File.Exists(ConfigurationPath));
        }

        public async Task<Dictionary<string, object>> GetRawConfigurationAsync()
        {
            try
            {
                if (!await ConfigurationExistsAsync())
                {
                    return new Dictionary<string, object>();
                }

                var json = await File.ReadAllTextAsync(ConfigurationPath);
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<Dictionary<string, object>>(json, options) 
                       ?? new Dictionary<string, object>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading raw plugin configuration for {_pluginId}: {ex.Message}");
                return new Dictionary<string, object>();
            }
        }

        public async Task SaveRawConfigurationAsync(Dictionary<string, object> configuration)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    WriteIndented = true
                };

                var json = JsonSerializer.Serialize(configuration, options);
                await File.WriteAllTextAsync(ConfigurationPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving raw plugin configuration for {_pluginId}: {ex.Message}");
                throw;
            }
        }
    }
}

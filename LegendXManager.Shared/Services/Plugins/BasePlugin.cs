using Microsoft.AspNetCore.Components;

namespace LegendXManager.Shared.Services.Plugins
{
    public abstract class BasePlugin : IPlugin
    {
        protected PluginMetadata _metadata;

        protected BasePlugin(PluginMetadata metadata)
        {
            _metadata = metadata;
            Settings = new Dictionary<string, object>();
        }

        public virtual string Id => _metadata.Id;
        public virtual string Name => _metadata.Name;
        public virtual string Description => _metadata.Description;
        public virtual string Version => _metadata.Version;
        public virtual string Author => _metadata.Author;
        public virtual string Category => _metadata.Category;
        public virtual string Icon => _metadata.Icon;
        public virtual PluginType Type => _metadata.Type;
        public virtual bool IsEnabled { get; set; } = true;
        public virtual Dictionary<string, object> Settings { get; }

        public abstract Type ComponentType { get; }

        public virtual Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        public virtual Task<bool> CanActivateAsync()
        {
            return Task.FromResult(IsEnabled);
        }

        public virtual RenderFragment GetMenuItems()
        {
            return builder => { };
        }

        protected virtual bool RequiresAuthentication => _metadata.RequiresAuthentication;
        protected virtual string[] RequiredRoles => _metadata.RequiredRoles;
        protected virtual string[] RequiredPermissions => _metadata.RequiredPermissions;
    }
}

using System.Text.Json;

namespace LegendXManager.Shared.Services.Plugins
{
    public interface IPluginConfiguration
    {
        string ConfigurationPath { get; }
        Task<T?> GetConfigurationAsync<T>() where T : class;
        Task SaveConfigurationAsync<T>(T configuration) where T : class;
        Task<bool> ConfigurationExistsAsync();
        Task<Dictionary<string, object>> GetRawConfigurationAsync();
        Task SaveRawConfigurationAsync(Dictionary<string, object> configuration);
    }

    public class PluginConfigurationBase
    {
        public string PluginId { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0.0";
        public bool Enabled { get; set; } = true;
        public DateTime LastModified { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Settings { get; set; } = new();
        public Dictionary<string, string> Environment { get; set; } = new();
        public PluginPermissions Permissions { get; set; } = new();
        public PluginUI UI { get; set; } = new();
        public PluginFeatures Features { get; set; } = new();
    }

    public class PluginPermissions
    {
        public string[] RequiredRoles { get; set; } = Array.Empty<string>();
        public string[] RequiredPermissions { get; set; } = Array.Empty<string>();
        public bool RequiresAuthentication { get; set; } = true;
        public string[] AllowedUsers { get; set; } = Array.Empty<string>();
        public string[] DeniedUsers { get; set; } = Array.Empty<string>();
    }

    public class PluginUI
    {
        public string Theme { get; set; } = "dark";
        public string Layout { get; set; } = "default";
        public Dictionary<string, string> Colors { get; set; } = new();
        public Dictionary<string, object> CustomStyles { get; set; } = new();
        public bool ShowInNavigation { get; set; } = true;
        public int NavigationOrder { get; set; } = 100;
        public string NavigationIcon { get; set; } = "bi bi-puzzle";
        public string NavigationCategory { get; set; } = "General";
    }

    public class PluginFeatures
    {
        public bool EnableLogging { get; set; } = true;
        public bool EnableCaching { get; set; } = true;
        public bool EnableAnalytics { get; set; } = false;
        public bool EnableNotifications { get; set; } = true;
        public bool EnableExport { get; set; } = true;
        public bool EnableImport { get; set; } = true;
        public Dictionary<string, bool> CustomFeatures { get; set; } = new();
    }
}

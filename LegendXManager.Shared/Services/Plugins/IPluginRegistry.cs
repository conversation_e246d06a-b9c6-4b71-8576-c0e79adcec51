namespace LegendXManager.Shared.Services.Plugins
{
    public interface IPluginRegistry
    {
        IEnumerable<IPlugin> GetAllPlugins();
        IEnumerable<IPlugin> GetPluginsByCategory(string category);
        IEnumerable<IPlugin> GetPluginsByType(PluginType type);
        IPlugin? GetPlugin(string id);
        void RegisterPlugin(IPlugin plugin);
        void UnregisterPlugin(string id);
        bool IsPluginRegistered(string id);
        IEnumerable<string> GetCategories();
        Task InitializeAllPluginsAsync();
        
        event EventHandler<PluginEventArgs>? PluginRegistered;
        event EventHandler<PluginEventArgs>? PluginUnregistered;
        event EventHandler<PluginEventArgs>? PluginStateChanged;
    }

    public class PluginEventArgs : EventArgs
    {
        public IPlugin Plugin { get; }
        
        public PluginEventArgs(IPlugin plugin)
        {
            Plugin = plugin;
        }
    }
}

@using LegendXManager.Shared.Models
@using LegendXManager.Shared.Services
@inject ICatalogService CatalogService

<div class="catalog-management">
    <div class="catalog-header">
        <div class="header-left">
            <h2><i class="bi bi-grid-3x3-gap"></i> Catalog Management</h2>
            <p class="text-muted">Manage your product catalog and categories</p>
        </div>
        <div class="header-right">
            <button class="btn btn-primary" @onclick="ShowAddCatalogModal">
                <i class="bi bi-plus-circle"></i> Add Product
            </button>
        </div>
    </div>

    <div class="catalog-filters">
        <div class="row">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" placeholder="Search products..." @bind="searchTerm" @oninput="OnSearchChanged" />
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" @bind="selectedCategory" @onchange="OnCategoryChanged">
                    <option value="">All Categories</option>
                    @foreach (var category in categories)
                    {
                        <option value="@category.Name">@category.Name</option>
                    }
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" @bind="sortBy" @onchange="OnSortChanged">
                    <option value="name">Name</option>
                    <option value="price">Price</option>
                    <option value="category">Category</option>
                    <option value="stock">Stock</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="btn-group" role="group">
                    <button type="button" class="btn @(viewMode == "grid" ? "btn-primary" : "btn-outline-secondary")" @onclick='() => SetViewMode("grid")'>
                        <i class="bi bi-grid-3x3"></i>
                    </button>
                    <button type="button" class="btn @(viewMode == "list" ? "btn-primary" : "btn-outline-secondary")" @onclick='() => SetViewMode("list")'>
                        <i class="bi bi-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center p-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading catalogs...</p>
        </div>
    }
    else if (filteredCatalogs.Any())
    {
        @if (viewMode == "grid")
        {
            <div class="catalog-grid">
                @foreach (var catalog in filteredCatalogs)
                {
                    <div class="catalog-card">
                        <div class="card">
                            <div class="card-img-placeholder">
                                <i class="bi bi-image"></i>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">@catalog.Name</h6>
                                <p class="card-text text-muted">@catalog.Description</p>
                                <div class="catalog-meta">
                                    <span class="badge bg-secondary">@catalog.Category</span>
                                    <span class="price">$@catalog.Price.ToString("F2")</span>
                                </div>
                                <div class="catalog-stock">
                                    <small class="text-muted">Stock: @catalog.StockQuantity</small>
                                    <small class="text-muted">SKU: @catalog.SKU</small>
                                </div>
                                <div class="card-actions">
                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => EditCatalog(catalog)">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteCatalog(catalog.Id)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="catalog-list">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>SKU</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var catalog in filteredCatalogs)
                            {
                                <tr>
                                    <td>
                                        <div>
                                            <strong>@catalog.Name</strong>
                                            <br>
                                            <small class="text-muted">@catalog.Description</small>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-secondary">@catalog.Category</span></td>
                                    <td><strong>$@catalog.Price.ToString("F2")</strong></td>
                                    <td>
                                        <span class="@(catalog.StockQuantity < 10 ? "text-danger" : "text-success")">
                                            @catalog.StockQuantity
                                        </span>
                                    </td>
                                    <td><code>@catalog.SKU</code></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" @onclick="() => EditCatalog(catalog)">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" @onclick="() => DeleteCatalog(catalog.Id)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        }
    }
    else
    {
        <div class="empty-state">
            <div class="text-center p-5">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <h4 class="mt-3">No products found</h4>
                <p class="text-muted">Try adjusting your search criteria or add a new product.</p>
                <button class="btn btn-primary" @onclick="ShowAddCatalogModal">
                    <i class="bi bi-plus-circle"></i> Add First Product
                </button>
            </div>
        </div>
    }
</div>

@code {
    private List<Catalog> catalogs = new();
    private List<Catalog> filteredCatalogs = new();
    private List<CatalogCategory> categories = new();
    private bool isLoading = true;
    private string searchTerm = string.Empty;
    private string selectedCategory = string.Empty;
    private string sortBy = "name";
    private string viewMode = "grid";

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            catalogs = await CatalogService.GetCatalogsAsync();
            categories = await CatalogService.GetCategoriesAsync();
            ApplyFilters();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading catalog data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ApplyFilters()
    {
        var filtered = catalogs.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            filtered = filtered.Where(c => 
                c.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                c.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                c.Tags.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        // Apply category filter
        if (!string.IsNullOrWhiteSpace(selectedCategory))
        {
            filtered = filtered.Where(c => c.Category.Equals(selectedCategory, StringComparison.OrdinalIgnoreCase));
        }

        // Apply sorting
        filtered = sortBy switch
        {
            "price" => filtered.OrderBy(c => c.Price),
            "category" => filtered.OrderBy(c => c.Category).ThenBy(c => c.Name),
            "stock" => filtered.OrderBy(c => c.StockQuantity),
            _ => filtered.OrderBy(c => c.Name)
        };

        filteredCatalogs = filtered.ToList();
    }

    private async Task OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;
        ApplyFilters();
        await Task.CompletedTask;
    }

    private async Task OnCategoryChanged(ChangeEventArgs e)
    {
        selectedCategory = e.Value?.ToString() ?? string.Empty;
        ApplyFilters();
        await Task.CompletedTask;
    }

    private async Task OnSortChanged(ChangeEventArgs e)
    {
        sortBy = e.Value?.ToString() ?? "name";
        ApplyFilters();
        await Task.CompletedTask;
    }

    private void SetViewMode(string mode)
    {
        viewMode = mode;
    }

    private void ShowAddCatalogModal()
    {
        // TODO: Implement add catalog modal
        Console.WriteLine("Add catalog modal would open here");
    }

    private void EditCatalog(Catalog catalog)
    {
        // TODO: Implement edit catalog modal
        Console.WriteLine($"Edit catalog: {catalog.Name}");
    }

    private async Task DeleteCatalog(int catalogId)
    {
        if (await CatalogService.DeleteCatalogAsync(catalogId))
        {
            await LoadData();
        }
    }
}

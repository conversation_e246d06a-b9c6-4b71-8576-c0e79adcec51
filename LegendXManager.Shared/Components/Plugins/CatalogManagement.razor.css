.catalog-management {
    padding: 20px;
    background-color: #1e1e1e;
    color: #d4d4d4;
    min-height: 100%;
}

.catalog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #3e3e42;
}

.catalog-header h2 {
    color: #ffffff;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.catalog-header h2 i {
    color: #007acc;
    margin-right: 10px;
}

.catalog-header p {
    margin: 5px 0 0 0;
    font-size: 14px;
}

.catalog-filters {
    background-color: #252526;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #3e3e42;
}

.catalog-filters .form-control,
.catalog-filters .form-select {
    background-color: #2d2d30;
    border: 1px solid #3e3e42;
    color: #d4d4d4;
}

.catalog-filters .form-control:focus,
.catalog-filters .form-select:focus {
    background-color: #2d2d30;
    border-color: #007acc;
    color: #d4d4d4;
    box-shadow: 0 0 0 0.2rem rgba(0, 122, 204, 0.25);
}

.catalog-filters .input-group-text {
    background-color: #2d2d30;
    border: 1px solid #3e3e42;
    color: #cccccc;
}

.catalog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.catalog-card .card {
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 8px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.catalog-card .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border-color: #007acc;
}

.card-img-placeholder {
    height: 150px;
    background-color: #2d2d30;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #3e3e42;
}

.card-img-placeholder i {
    font-size: 48px;
    color: #666;
}

.catalog-card .card-body {
    padding: 15px;
}

.catalog-card .card-title {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 8px;
}

.catalog-card .card-text {
    font-size: 13px;
    margin-bottom: 12px;
    line-height: 1.4;
}

.catalog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.catalog-meta .price {
    font-weight: 600;
    color: #4fc1ff;
    font-size: 16px;
}

.catalog-stock {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.catalog-stock small {
    font-size: 11px;
}

.card-actions {
    display: flex;
    gap: 8px;
}

.catalog-list .table {
    background-color: #252526;
    color: #d4d4d4;
    border: 1px solid #3e3e42;
    border-radius: 8px;
    overflow: hidden;
}

.catalog-list .table th {
    background-color: #2d2d30;
    border-color: #3e3e42;
    color: #cccccc;
    font-weight: 600;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.catalog-list .table td {
    border-color: #3e3e42;
    vertical-align: middle;
}

.catalog-list .table tbody tr:hover {
    background-color: #2a2d2e;
}

.empty-state {
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 8px;
    margin-top: 20px;
}

.empty-state i {
    color: #666;
}

.empty-state h4 {
    color: #cccccc;
}

.btn-primary {
    background-color: #007acc;
    border-color: #007acc;
}

.btn-primary:hover {
    background-color: #005a9e;
    border-color: #005a9e;
}

.btn-outline-primary {
    color: #007acc;
    border-color: #007acc;
}

.btn-outline-primary:hover {
    background-color: #007acc;
    border-color: #007acc;
    color: white;
}

.btn-outline-secondary {
    color: #cccccc;
    border-color: #3e3e42;
}

.btn-outline-secondary:hover {
    background-color: #3e3e42;
    border-color: #3e3e42;
    color: white;
}

.btn-outline-danger {
    color: #f85149;
    border-color: #f85149;
}

.btn-outline-danger:hover {
    background-color: #f85149;
    border-color: #f85149;
    color: white;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

.spinner-border {
    color: #007acc;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .catalog-management {
        padding: 15px;
    }
    
    .catalog-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .catalog-filters .row > div {
        margin-bottom: 10px;
    }
    
    .catalog-grid {
        grid-template-columns: 1fr;
    }
}

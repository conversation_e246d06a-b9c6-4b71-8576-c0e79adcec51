namespace LegendXManager.Shared.Models
{
    public class Catalog
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
        public string Tags { get; set; } = string.Empty;
        public int StockQuantity { get; set; }
        public string SKU { get; set; } = string.Empty;
    }

    public class CatalogCategory
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = "bi bi-folder";
        public bool IsActive { get; set; } = true;
    }
}

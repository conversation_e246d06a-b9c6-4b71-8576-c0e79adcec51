@page "/"
@page "/login"
@using LegendXManager.Shared.Components.Auth


<PageTitle>Sign In - LegendX Manager</PageTitle>

<LoginForm ReturnUrl="@ReturnUrl" OnLoginStateChanged="HandleLoginStateChanged" />

@code {
    [Parameter]
    [SupplyParameterFromQuery]
    public string? ReturnUrl { get; set; }

    private void HandleLoginStateChanged(bool isLoggedIn)
    {
        if (isLoggedIn)
        {
            // Additional logic after successful login if needed
            Console.WriteLine("User successfully logged in");
        }
    }
}

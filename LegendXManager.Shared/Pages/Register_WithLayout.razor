@page "/register"
@using LegendXManager.Shared.Components.Auth
@layout LegendXManager.Shared.Layout.LoginLayout

<PageTitle>Create Account - LegendX Manager</PageTitle>

<RegisterForm OnRegistrationStateChanged="HandleRegistrationStateChanged" />

@code {
    private void HandleRegistrationStateChanged(bool isRegistered)
    {
        if (isRegistered)
        {
            // Additional logic after successful registration if needed
            Console.WriteLine("User successfully registered");
        }
    }
}
